{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\components\\TenantForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\components\\TenantForm.vue", "mtime": 1753856341584}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import \"core-js/modules/es7.object.get-own-property-descriptors\";\nimport \"core-js/modules/web.dom.iterable\";\nimport \"core-js/modules/es6.object.keys\";\nimport _defineProperty from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nexport default {\n  name: 'TenantForm',\n  props: {\n    // 表单数据\n    value: {\n      type: Object,\n      default: function _default() {\n        return {\n          id: null,\n          tenantCode: '',\n          tenantName: '',\n          tenantAdmin: '',\n          comments: '',\n          isSelected: false\n        };\n      }\n    },\n    // 是否为编辑模式\n    isEdit: {\n      type: Boolean,\n      default: false\n    },\n    // 提交时的loading状态\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data: function data() {\n    return {\n      formData: {\n        id: null,\n        tenantCode: '',\n        tenantName: '',\n        tenantAdmin: '',\n        comments: '',\n        isSelected: false\n      },\n      rules: {\n        tenantCode: [{\n          required: true,\n          message: '请输入租户编码',\n          trigger: 'blur'\n        }, {\n          min: 2,\n          max: 32,\n          message: '长度在 2 到 32 个字符',\n          trigger: 'blur'\n        }],\n        tenantName: [{\n          required: true,\n          message: '请输入租户名称',\n          trigger: 'blur'\n        }, {\n          min: 2,\n          max: 50,\n          message: '长度在 2 到 50 个字符',\n          trigger: 'blur'\n        }],\n        // tenantAdmin: [\n        //   {min: 0, max: 50, message: '长度不能超过 50 个字符', trigger: 'blur'}\n        // ],\n        comments: [{\n          max: 200,\n          message: '长度不能超过 200 个字符',\n          trigger: 'blur'\n        }]\n      },\n      updateTimer: null // 用于防抖的定时器\n    };\n  },\n  watch: {\n    value: {\n      handler: function handler(newVal, oldVal) {\n        // 避免不必要的更新，只在值真正改变时才更新\n        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {\n          this.formData = _objectSpread({}, newVal);\n        }\n      },\n      immediate: true,\n      deep: false // 改为浅层监听，提高性能\n    }\n  },\n  methods: {\n    // 处理表单字段变化，使用防抖优化性能\n    handleFieldChange: function handleFieldChange(field, value) {\n      var _this = this;\n      // 使用 Vue.set 确保响应式更新\n      this.$set(this.formData, field, value);\n\n      // 防抖发送更新事件，避免频繁触发\n      if (this.updateTimer) {\n        clearTimeout(this.updateTimer);\n      }\n      this.updateTimer = setTimeout(function () {\n        _this.$emit('input', _objectSpread({}, _this.formData));\n      }, 100);\n    },\n    // 提交表单\n    handleSubmit: function handleSubmit() {\n      var _this2 = this;\n      this.$refs.tenantForm.validate(function (valid) {\n        if (valid) {\n          _this2.$emit('submit', _this2.formData);\n        } else {\n          return false;\n        }\n      });\n    },\n    // 取消操作\n    handleCancel: function handleCancel() {\n      this.$emit('cancel');\n    },\n    // 重置表单\n    resetForm: function resetForm() {\n      this.$refs.tenantForm.resetFields();\n      this.formData = {\n        id: null,\n        tenantCode: '',\n        tenantName: '',\n        tenantAdmin: '',\n        comments: '',\n        isSelected: false\n      };\n    },\n    // 验证表单\n    validate: function validate() {\n      var _this3 = this;\n      return new Promise(function (resolve) {\n        _this3.$refs.tenantForm.validate(function (valid) {\n          resolve(valid);\n        });\n      });\n    }\n  },\n  beforeDestroy: function beforeDestroy() {\n    // 清理定时器，防止内存泄漏\n    if (this.updateTimer) {\n      clearTimeout(this.updateTimer);\n    }\n  }\n};", {"version": 3, "names": ["name", "props", "value", "type", "Object", "default", "id", "tenantCode", "tenantName", "tenantAdmin", "comments", "isSelected", "isEdit", "Boolean", "loading", "data", "formData", "rules", "required", "message", "trigger", "min", "max", "updateTimer", "watch", "handler", "newVal", "oldVal", "JSON", "stringify", "_objectSpread", "immediate", "deep", "methods", "handleFieldChange", "field", "_this", "$set", "clearTimeout", "setTimeout", "$emit", "handleSubmit", "_this2", "$refs", "tenantForm", "validate", "valid", "handleCancel", "resetForm", "resetFields", "_this3", "Promise", "resolve", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/bysc_system/views/tenant/components/TenantForm.vue"], "sourcesContent": ["\n<template>\n  <div class=\"tenant-form\">\n    <el-form\n      :model=\"formData\"\n      :rules=\"rules\"\n      ref=\"tenantForm\"\n      label-width=\"120px\"\n      class=\"tenant-form-content\"\n    >\n      <el-form-item label=\"租户编码：\" prop=\"tenantCode\">\n        <el-input\n          :value=\"formData.tenantCode\"\n          @input=\"handleFieldChange('tenantCode', $event.trim())\"\n          placeholder=\"请输入租户编码\"\n          maxlength=\"32\"\n          :disabled=\"isEdit\"\n        ></el-input>\n      </el-form-item>\n\n      <el-form-item label=\"*租户名称：\" prop=\"tenantName\">\n        <el-input\n          :value=\"formData.tenantName\"\n          @input=\"handleFieldChange('tenantName', $event.trim())\"\n          placeholder=\"请输入租户名称\"\n          maxlength=\"50\"\n        ></el-input>\n      </el-form-item>\n\n      <!-- <el-form-item label=\"租户管理员：\" prop=\"tenantAdmin\">\n        <el-input\n          :value=\"formData.tenantAdmin\"\n          @input=\"handleFieldChange('tenantAdmin', $event.trim())\"\n          placeholder=\"请输入租户管理员\"\n          maxlength=\"50\"\n        ></el-input>\n      </el-form-item> -->\n\n      <el-form-item label=\"租户备注：\" prop=\"comments\">\n        <el-input\n          :value=\"formData.comments\"\n          @input=\"handleFieldChange('comments', $event.trim())\"\n          type=\"textarea\"\n          :rows=\"4\"\n          placeholder=\"请输入租户备注\"\n          maxlength=\"200\"\n          show-word-limit\n        ></el-input>\n      </el-form-item>\n\n      <el-form-item label=\"是否选中：\" prop=\"isSelected\">\n        <el-switch\n          :value=\"formData.isSelected\"\n          @change=\"handleFieldChange('isSelected', $event)\"\n          active-text=\"是\"\n          inactive-text=\"否\"\n        ></el-switch>\n      </el-form-item>\n    </el-form>\n\n    <div class=\"form-actions\">\n      <el-button type=\"primary\" @click=\"handleSubmit\" :loading=\"loading\">\n        {{ isEdit ? '保存' : '添加' }}\n      </el-button>\n      <el-button @click=\"handleCancel\">取消</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'TenantForm',\n  props: {\n    // 表单数据\n    value: {\n      type: Object,\n      default: () => ({\n        id: null,\n        tenantCode: '',\n        tenantName: '',\n        tenantAdmin: '',\n        comments: '',\n        isSelected: false\n      })\n    },\n    // 是否为编辑模式\n    isEdit: {\n      type: Boolean,\n      default: false\n    },\n    // 提交时的loading状态\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      formData: {\n        id: null,\n        tenantCode: '',\n        tenantName: '',\n        tenantAdmin: '',\n        comments: '',\n        isSelected: false\n      },\n      rules: {\n        tenantCode: [\n          {required: true, message: '请输入租户编码', trigger: 'blur'},\n          {min: 2, max: 32, message: '长度在 2 到 32 个字符', trigger: 'blur'}\n        ],\n        tenantName: [\n          {required: true, message: '请输入租户名称', trigger: 'blur'},\n          {min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur'}\n        ],\n        // tenantAdmin: [\n        //   {min: 0, max: 50, message: '长度不能超过 50 个字符', trigger: 'blur'}\n        // ],\n        comments: [\n          {max: 200, message: '长度不能超过 200 个字符', trigger: 'blur'}\n        ]\n      },\n      updateTimer: null // 用于防抖的定时器\n    };\n  },\n  watch: {\n    value: {\n      handler(newVal, oldVal) {\n        // 避免不必要的更新，只在值真正改变时才更新\n        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {\n          this.formData = {...newVal};\n        }\n      },\n      immediate: true,\n      deep: false // 改为浅层监听，提高性能\n    }\n  },\n  methods: {\n    // 处理表单字段变化，使用防抖优化性能\n    handleFieldChange(field, value) {\n      // 使用 Vue.set 确保响应式更新\n      this.$set(this.formData, field, value);\n\n      // 防抖发送更新事件，避免频繁触发\n      if (this.updateTimer) {\n        clearTimeout(this.updateTimer);\n      }\n      this.updateTimer = setTimeout(() => {\n        this.$emit('input', {...this.formData});\n      }, 100);\n    },\n\n    // 提交表单\n    handleSubmit() {\n      this.$refs.tenantForm.validate(valid => {\n        if (valid) {\n          this.$emit('submit', this.formData);\n        } else {\n          return false;\n        }\n      });\n    },\n\n    // 取消操作\n    handleCancel() {\n      this.$emit('cancel');\n    },\n\n    // 重置表单\n    resetForm() {\n      this.$refs.tenantForm.resetFields();\n      this.formData = {\n        id: null,\n        tenantCode: '',\n        tenantName: '',\n        tenantAdmin: '',\n        comments: '',\n        isSelected: false\n      };\n    },\n\n    // 验证表单\n    validate() {\n      return new Promise(resolve => {\n        this.$refs.tenantForm.validate(valid => {\n          resolve(valid);\n        });\n      });\n    }\n  },\n\n  beforeDestroy() {\n    // 清理定时器，防止内存泄漏\n    if (this.updateTimer) {\n      clearTimeout(this.updateTimer);\n    }\n  }\n};\n</script>\n\n<style lang=\"less\" scoped>\n.tenant-form {\n  padding: 20px;\n\n  .tenant-form-content {\n    .el-form-item {\n      margin-bottom: 20px;\n\n      .el-input,\n      .el-textarea {\n        width: 100%;\n      }\n    }\n  }\n\n  .form-actions {\n    text-align: center;\n    margin-top: 30px;\n\n    .el-button {\n      margin: 0 10px;\n      min-width: 80px;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;AAsEA;EACAA,IAAA;EACAC,KAAA;IACA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;UACAC,EAAA;UACAC,UAAA;UACAC,UAAA;UACAC,WAAA;UACAC,QAAA;UACAC,UAAA;QACA;MAAA;IACA;IACA;IACAC,MAAA;MACAT,IAAA,EAAAU,OAAA;MACAR,OAAA;IACA;IACA;IACAS,OAAA;MACAX,IAAA,EAAAU,OAAA;MACAR,OAAA;IACA;EACA;EACAU,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;QACAV,EAAA;QACAC,UAAA;QACAC,UAAA;QACAC,WAAA;QACAC,QAAA;QACAC,UAAA;MACA;MACAM,KAAA;QACAV,UAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,UAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACA;QACA;QACA;QACAV,QAAA,GACA;UAAAY,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAG,WAAA;IACA;EACA;EACAC,KAAA;IACAtB,KAAA;MACAuB,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA;QACA,IAAAC,IAAA,CAAAC,SAAA,CAAAH,MAAA,MAAAE,IAAA,CAAAC,SAAA,CAAAF,MAAA;UACA,KAAAX,QAAA,GAAAc,aAAA,KAAAJ,MAAA;QACA;MACA;MACAK,SAAA;MACAC,IAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,KAAA,EAAAjC,KAAA;MAAA,IAAAkC,KAAA;MACA;MACA,KAAAC,IAAA,MAAArB,QAAA,EAAAmB,KAAA,EAAAjC,KAAA;;MAEA;MACA,SAAAqB,WAAA;QACAe,YAAA,MAAAf,WAAA;MACA;MACA,KAAAA,WAAA,GAAAgB,UAAA;QACAH,KAAA,CAAAI,KAAA,UAAAV,aAAA,KAAAM,KAAA,CAAApB,QAAA;MACA;IACA;IAEA;IACAyB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAC,UAAA,CAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAJ,MAAA,CAAAF,KAAA,WAAAE,MAAA,CAAA1B,QAAA;QACA;UACA;QACA;MACA;IACA;IAEA;IACA+B,YAAA,WAAAA,aAAA;MACA,KAAAP,KAAA;IACA;IAEA;IACAQ,SAAA,WAAAA,UAAA;MACA,KAAAL,KAAA,CAAAC,UAAA,CAAAK,WAAA;MACA,KAAAjC,QAAA;QACAV,EAAA;QACAC,UAAA;QACAC,UAAA;QACAC,WAAA;QACAC,QAAA;QACAC,UAAA;MACA;IACA;IAEA;IACAkC,QAAA,WAAAA,SAAA;MAAA,IAAAK,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA;QACAF,MAAA,CAAAP,KAAA,CAAAC,UAAA,CAAAC,QAAA,WAAAC,KAAA;UACAM,OAAA,CAAAN,KAAA;QACA;MACA;IACA;EACA;EAEAO,aAAA,WAAAA,cAAA;IACA;IACA,SAAA9B,WAAA;MACAe,YAAA,MAAAf,WAAA;IACA;EACA;AACA", "ignoreList": []}]}